package providers

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/arien-ai/cli/prompts"
	"github.com/go-resty/resty/v2"
)

// DeepseekProvider implements the Provider interface for Deepseek API
type DeepseekProvider struct {
	client  *resty.Client
	apiKey  string
	baseURL string
	model   string
}

// NewDeepseekProvider creates a new Deepseek provider
func NewDeepseekProvider(apiKey, baseURL, model string) *DeepseekProvider {
	client := resty.New().
		SetTimeout(60 * time.Second).
		SetHeader("Content-Type", "application/json").
		SetHeader("Authorization", "Bearer "+apiKey)

	if baseURL == "" {
		baseURL = "https://api.deepseek.com/v1"
	}

	return &DeepseekProvider{
		client:  client,
		apiKey:  apiKey,
		baseURL: baseURL,
		model:   model,
	}
}

// Name returns the provider name
func (p *DeepseekProvider) Name() string {
	return "deepseek"
}

// Chat sends a message and returns the response
func (p *DeepseekProvider) Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
	if request.Model == "" {
		request.Model = p.model
	}

	url := fmt.Sprintf("%s/v1/chat/completions", p.baseURL)
	
	var response ChatResponse
	resp, err := p.client.R().
		SetContext(ctx).
		SetBody(request).
		SetResult(&response).
		Post(url)

	if err != nil {
		return nil, fmt.Errorf("deepseek API request failed: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("deepseek API error: %s", resp.String())
	}

	return &response, nil
}

// ChatStream sends a message and returns a streaming response
func (p *DeepseekProvider) ChatStream(ctx context.Context, request *ChatRequest) (<-chan *ChatStreamResponse, error) {
	if request.Model == "" {
		request.Model = p.model
	}
	request.Stream = true

	url := fmt.Sprintf("%s/v1/chat/completions", p.baseURL)
	
	responseChan := make(chan *ChatStreamResponse, 100)

	go func() {
		defer close(responseChan)

		resp, err := p.client.R().
			SetContext(ctx).
			SetBody(request).
			SetDoNotParseResponse(true).
			Post(url)

		if err != nil {
			responseChan <- &ChatStreamResponse{
				Error: fmt.Sprintf("deepseek API request failed: %v", err),
				Done:  true,
			}
			return
		}
		defer resp.RawBody().Close()

		if resp.IsError() {
			responseChan <- &ChatStreamResponse{
				Error: fmt.Sprintf("deepseek API error: %s", resp.String()),
				Done:  true,
			}
			return
		}

		scanner := bufio.NewScanner(resp.RawBody())
		for scanner.Scan() {
			line := scanner.Text()
			
			if !strings.HasPrefix(line, "data: ") {
				continue
			}
			
			data := strings.TrimPrefix(line, "data: ")
			if data == "[DONE]" {
				responseChan <- &ChatStreamResponse{Done: true}
				break
			}

			var streamResp ChatStreamResponse
			if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
				continue // Skip malformed responses
			}

			responseChan <- &streamResp
		}

		if err := scanner.Err(); err != nil {
			responseChan <- &ChatStreamResponse{
				Error: fmt.Sprintf("error reading stream: %v", err),
				Done:  true,
			}
		}
	}()

	return responseChan, nil
}

// IsAvailable checks if the provider is available
func (p *DeepseekProvider) IsAvailable(ctx context.Context) error {
	url := fmt.Sprintf("%s/v1/models", p.baseURL)
	
	resp, err := p.client.R().
		SetContext(ctx).
		Get(url)

	if err != nil {
		return fmt.Errorf("deepseek API not available: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("deepseek API error: %s", resp.String())
	}

	return nil
}

// GetModels returns available models
func (p *DeepseekProvider) GetModels(ctx context.Context) ([]Model, error) {
	url := fmt.Sprintf("%s/v1/models", p.baseURL)
	
	var response struct {
		Object string  `json:"object"`
		Data   []Model `json:"data"`
	}

	resp, err := p.client.R().
		SetContext(ctx).
		SetResult(&response).
		Get(url)

	if err != nil {
		return nil, fmt.Errorf("failed to get models: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("deepseek API error: %s", resp.String())
	}

	return response.Data, nil
}

// GetSystemPrompt returns the system prompt for Deepseek
func (p *DeepseekProvider) GetSystemPrompt() string {
	return prompts.SystemPrompt()
}
