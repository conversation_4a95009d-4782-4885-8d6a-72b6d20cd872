package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"time"

	"github.com/arien-ai/cli/prompts"
	"github.com/ollama/ollama/api"
)

// OllamaProvider implements the Provider interface for Ollama
type OllamaProvider struct {
	client *api.Client
	host   string
	model  string
}

// NewOllamaProvider creates a new Ollama provider
func NewOllamaProvider(host, model string) *OllamaProvider {
	if host == "" {
		host = "http://localhost:11434"
	}

	client, _ := api.ClientFromEnvironment()
	if client == nil {
		// Parse URL for the client
		u, err := url.Parse(host)
		if err != nil {
			// Fallback to default
			u, _ = url.Parse("http://localhost:11434")
		}
		client = api.NewClient(u, nil)
	}

	return &OllamaProvider{
		client: client,
		host:   host,
		model:  model,
	}
}

// Name returns the provider name
func (p *OllamaProvider) Name() string {
	return "ollama"
}

// Chat sends a message and returns the response
func (p *OllamaProvider) Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
	if request.Model == "" {
		request.Model = p.model
	}

	// Convert messages to Ollama format
	messages := make([]api.Message, len(request.Messages))
	for i, msg := range request.Messages {
		messages[i] = api.Message{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// Convert tools to Ollama format
	var tools []api.Tool
	if len(request.Tools) > 0 {
		tools = p.convertToolsToOllamaFormat(request.Tools)
	}

	chatRequest := &api.ChatRequest{
		Model:    request.Model,
		Messages: messages,
		Tools:    tools,
		Options: map[string]interface{}{
			"temperature": request.Temperature,
		},
	}

	if request.MaxTokens > 0 {
		chatRequest.Options["num_predict"] = request.MaxTokens
	}

	var response api.ChatResponse
	err := p.client.Chat(ctx, chatRequest, func(resp api.ChatResponse) error {
		response = resp
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("ollama chat request failed: %w", err)
	}

	// Convert response to standard format
	choices := []Choice{
		{
			Index: 0,
			Message: Message{
				Role:    response.Message.Role,
				Content: response.Message.Content,
			},
			FinishReason: "stop",
		},
	}

	// Handle tool calls if present
	if len(response.Message.ToolCalls) > 0 {
		toolCalls := make([]ToolCall, len(response.Message.ToolCalls))
		for i, tc := range response.Message.ToolCalls {
			args, _ := json.Marshal(tc.Function.Arguments)
			toolCalls[i] = ToolCall{
				ID:   fmt.Sprintf("call_%d", i),
				Type: "function",
				Function: ToolCallFunction{
					Name:      tc.Function.Name,
					Arguments: string(args),
				},
			}
		}
		choices[0].Message.ToolCalls = toolCalls
		choices[0].FinishReason = "tool_calls"
	}

	return &ChatResponse{
		ID:      fmt.Sprintf("chatcmpl-%d", time.Now().Unix()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   request.Model,
		Choices: choices,
		Usage: Usage{
			PromptTokens:     response.PromptEvalCount,
			CompletionTokens: response.EvalCount,
			TotalTokens:      response.PromptEvalCount + response.EvalCount,
		},
	}, nil
}

// ChatStream sends a message and returns a streaming response
func (p *OllamaProvider) ChatStream(ctx context.Context, request *ChatRequest) (<-chan *ChatStreamResponse, error) {
	if request.Model == "" {
		request.Model = p.model
	}

	// Convert messages to Ollama format
	messages := make([]api.Message, len(request.Messages))
	for i, msg := range request.Messages {
		messages[i] = api.Message{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// Convert tools to Ollama format (same as in Chat method)
	var tools []api.Tool
	if len(request.Tools) > 0 {
		tools = p.convertToolsToOllamaFormat(request.Tools)
	}

	chatRequest := &api.ChatRequest{
		Model:    request.Model,
		Messages: messages,
		Tools:    tools,
		Stream:   &[]bool{true}[0],
		Options: map[string]interface{}{
			"temperature": request.Temperature,
		},
	}

	if request.MaxTokens > 0 {
		chatRequest.Options["num_predict"] = request.MaxTokens
	}

	responseChan := make(chan *ChatStreamResponse, 100)

	go func() {
		defer close(responseChan)

		err := p.client.Chat(ctx, chatRequest, func(resp api.ChatResponse) error {
			streamResp := &ChatStreamResponse{
				ID:      fmt.Sprintf("chatcmpl-%d", time.Now().Unix()),
				Object:  "chat.completion.chunk",
				Created: time.Now().Unix(),
				Model:   request.Model,
				Choices: []StreamChoice{
					{
						Index: 0,
						Delta: MessageDelta{
							Role:    resp.Message.Role,
							Content: resp.Message.Content,
						},
					},
				},
				Done: resp.Done,
			}

			// Handle tool calls in streaming
			if len(resp.Message.ToolCalls) > 0 {
				toolCalls := make([]ToolCall, len(resp.Message.ToolCalls))
				for i, tc := range resp.Message.ToolCalls {
					args, _ := json.Marshal(tc.Function.Arguments)
					toolCalls[i] = ToolCall{
						ID:   fmt.Sprintf("call_%d", i),
						Type: "function",
						Function: ToolCallFunction{
							Name:      tc.Function.Name,
							Arguments: string(args),
						},
					}
				}
				streamResp.Choices[0].Delta.ToolCalls = toolCalls
				streamResp.Choices[0].FinishReason = "tool_calls"
			}

			if resp.Done {
				streamResp.Choices[0].FinishReason = "stop"
			}

			responseChan <- streamResp
			return nil
		})

		if err != nil {
			responseChan <- &ChatStreamResponse{
				Error: fmt.Sprintf("ollama chat stream failed: %v", err),
				Done:  true,
			}
		}
	}()

	return responseChan, nil
}

// IsAvailable checks if the provider is available
func (p *OllamaProvider) IsAvailable(ctx context.Context) error {
	err := p.client.Heartbeat(ctx)
	if err != nil {
		return fmt.Errorf("ollama not available: %w", err)
	}
	return nil
}

// GetModels returns available models
func (p *OllamaProvider) GetModels(ctx context.Context) ([]Model, error) {
	listResp, err := p.client.List(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list models: %w", err)
	}

	models := make([]Model, len(listResp.Models))
	for i, model := range listResp.Models {
		models[i] = Model{
			ID:      model.Name,
			Object:  "model",
			Created: model.ModifiedAt.Unix(),
			OwnedBy: "ollama",
		}
	}

	return models, nil
}

// GetSystemPrompt returns the system prompt for Ollama
func (p *OllamaProvider) GetSystemPrompt() string {
	return prompts.SystemPrompt()
}

// convertToolsToOllamaFormat converts standard tools to Ollama API format
func (p *OllamaProvider) convertToolsToOllamaFormat(tools []Tool) []api.Tool {
	ollamaTools := make([]api.Tool, len(tools))

	for i, tool := range tools {
		// Convert parameters to Ollama format
		params := api.ToolFunction{
			Name:        tool.Function.Name,
			Description: tool.Function.Description,
		}

		// For now, just pass the parameters as-is and let Ollama handle the conversion
		// This is a simplified approach that should work for basic tool definitions
		if tool.Function.Parameters != nil {
			// Convert to JSON and back to ensure proper structure
			paramBytes, _ := json.Marshal(tool.Function.Parameters)
			var paramStruct struct {
				Type       string                 `json:"type"`
				Properties map[string]interface{} `json:"properties"`
				Required   []string               `json:"required"`
			}
			json.Unmarshal(paramBytes, &paramStruct)

			params.Parameters.Type = paramStruct.Type
			if params.Parameters.Type == "" {
				params.Parameters.Type = "object"
			}
			params.Parameters.Required = paramStruct.Required

			// For now, skip the complex property conversion and let Ollama handle it
			// This is a simplified approach that should work for most cases
		}

		ollamaTools[i] = api.Tool{
			Type:     tool.Type,
			Function: params,
		}
	}

	return ollamaTools
}
